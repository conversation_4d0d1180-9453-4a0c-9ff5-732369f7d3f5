import { NextRequest, NextResponse } from 'next/server';
import { processWithGroq } from '../../../lib/tools/groq-ai';
import { z } from 'zod';
import { AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';

// Enhanced Zod schema for LLM team selection response with strict validation
const TeamSelectionResponseSchema = z.object({
  selectedTeam: z.enum(['Ag001', 'Ag002', 'Ag003', 'Ag004', 'Ag005', 'Ag006']),
  rationale: z.string().min(20, 'Rationale must be at least 20 characters long'),
  confidence: z.number().min(0).max(1).default(0.8),
  reasoning: z.string().min(10, 'Reasoning must be at least 10 characters long').optional()
});

// Team information for comprehensive LLM context
const TEAM_DESCRIPTIONS = {
  'Ag001': {
    name: 'Marketing Team',
    expertise: 'Marketing strategy, content creation, brand management, market research, campaigns, customer acquisition, digital marketing, SEO, social media, advertising, promotion, lead generation, brand awareness',
    deliverables: 'Marketing plans, content strategies, campaign designs, brand guidelines, market research reports, customer acquisition strategies'
  },
  'Ag002': {
    name: 'Research Team',
    expertise: 'Data collection, analysis, literature reviews, research reports, surveys, statistical analysis, market research, competitive analysis, user research, academic research, data insights',
    deliverables: 'Research reports, data analysis, survey results, literature reviews, market studies, statistical findings, research methodologies'
  },
  'Ag003': {
    name: 'Software Design Team',
    expertise: 'Software development, UI/UX design, coding, technical implementation, mobile applications, web applications, system architecture, database design, API development, frontend, backend',
    deliverables: 'Software applications, technical specifications, system designs, code implementations, user interfaces, technical documentation'
  },
  'Ag004': {
    name: 'Sales Team',
    expertise: 'Sales strategies, client relationships, proposal development, revenue generation, CRM management, business development, lead conversion, account management, negotiation, sales processes',
    deliverables: 'Sales strategies, client proposals, revenue plans, CRM implementations, sales processes, account management systems'
  },
  'Ag005': {
    name: 'Business Analysis Team',
    expertise: 'Strategic planning, process analysis, requirements engineering, workflow optimization, business consulting, operational efficiency, business transformation, process improvement, strategic analysis',
    deliverables: 'Business requirements, process maps, strategic plans, workflow optimizations, business cases, operational improvements'
  },
  'Ag006': {
    name: 'Investigative Research Team',
    expertise: 'Comprehensive investigative analysis, multi-source verification, deep-dive research, exposing irregularities, journalistic investigation, fact-checking, corruption analysis, financial fraud detection, whistleblowing support',
    deliverables: 'Investigative reports, fact-checking analyses, corruption assessments, fraud detection reports, whistleblowing documentation, comprehensive investigations'
  }
};

/**
 * Test endpoint for team selection without authentication
 * This is for testing purposes only
 */
async function selectTeamWithLLM(
  description: string, 
  projectTitle: string = '', 
  priority: string = 'Medium',
  category: string = 'Unknown',
  requirements: string = '',
  expectedOutcome: string = ''
): Promise<{
  selectedTeams: AgenticTeamId[];
  rationale: string;
  confidence: number;
  reasoning?: string;
}> {
  
  // Validate input parameters
  if (!description || description.trim().length < 5) {
    throw new Error('Project description is too short or empty. Please provide a meaningful description for accurate team selection.');
  }

  // Construct comprehensive project context
  const projectContext = `
PROJECT TITLE: ${projectTitle || 'Not specified'}
DESCRIPTION: ${description}
PRIORITY: ${priority}
CATEGORY: ${category}
REQUIREMENTS: ${requirements || 'Not specified'}
EXPECTED OUTCOME: ${expectedOutcome || 'Not specified'}
`.trim();

  // Enhanced prompt with comprehensive team information and selection criteria
  const prompt = `You are an expert PMO (Project Management Office) team assignment specialist with deep knowledge of organizational capabilities and project requirements. Your task is to analyze the project details and select the single most appropriate team.

PROJECT DETAILS:
${projectContext}

AVAILABLE TEAMS WITH DETAILED CAPABILITIES:

${Object.entries(TEAM_DESCRIPTIONS).map(([id, info]) => `
${id} (${info.name}):
• Expertise: ${info.expertise}
• Typical Deliverables: ${info.deliverables}
`).join('\n')}

SELECTION METHODOLOGY:
1. Analyze the primary objective and scope of the project
2. Identify the core skills and expertise required
3. Match project requirements to team capabilities
4. Consider the expected deliverables and outcomes
5. Evaluate which team can provide the highest value and success probability

CRITICAL REQUIREMENTS:
- Select EXACTLY ONE team that best matches the project requirements
- Provide detailed rationale based on specific project needs
- Assign a confidence score (0.0 to 1.0) based on the match quality
- Consider both primary and secondary project aspects

RESPONSE FORMAT:
You must respond with ONLY valid JSON in this exact structure:
{
  "selectedTeam": "Ag00X",
  "rationale": "Comprehensive explanation of why this specific team was selected, referencing specific project requirements and team capabilities",
  "confidence": 0.95,
  "reasoning": "Step-by-step analysis of the selection process and key factors that influenced the decision"
}

EXAMPLES OF GOOD RATIONALE:
- "Selected Ag003 (Software Design Team) because the project requires developing a mobile application with real-time tracking capabilities, which directly aligns with their expertise in mobile development, database design, and system architecture."
- "Chose Ag001 (Marketing Team) as the project focuses on creating a comprehensive digital marketing campaign for product launch, requiring their specialized skills in campaign design, brand management, and customer acquisition strategies."

Analyze the project thoroughly and provide your selection:`;

  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Test Team Selection: Attempt ${attempt}/${maxRetries} - Calling Groq AI`);
      console.log(`Test Team Selection: Project context length: ${projectContext.length} characters`);

      // Call Groq AI with optimized parameters for consistent results
      const response = await processWithGroq({
        prompt,
        model: 'deepseek-r1-distill-llama-70b',
        modelOptions: {
          temperature: 0.1, // Very low temperature for consistent results
          max_tokens: 800,
          top_p: 0.9,
          frequency_penalty: 0.0,
          presence_penalty: 0.0
        }
      });

      console.log(`Test Team Selection: Raw LLM response (attempt ${attempt}):`, response);

      // Enhanced JSON extraction with multiple parsing strategies
      let parsedResponse;
      try {
        // Strategy 1: Clean and extract JSON directly
        const cleanResponse = response
          .replace(/```json\s*/gi, '')
          .replace(/```\s*/g, '')
          .replace(/^[^{]*/, '')
          .replace(/}[^}]*$/, '}')
          .trim();

        // Strategy 2: Use regex to find JSON object
        const jsonRegex = /\{[\s\S]*?\}/;
        const jsonMatch = cleanResponse.match(jsonRegex);
        
        if (jsonMatch) {
          parsedResponse = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON object found in response');
        }

        console.log(`Test Team Selection: Successfully parsed JSON (attempt ${attempt}):`, parsedResponse);
      } catch (parseError) {
        console.error(`Test Team Selection: JSON parsing failed (attempt ${attempt}):`, parseError);
        console.error(`Test Team Selection: Response was:`, response);
        
        if (attempt === maxRetries) {
          const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
          throw new Error(`Failed to parse LLM response as JSON after ${maxRetries} attempts. Last error: ${errorMessage}`);
        }
        
        // Continue to next attempt
        lastError = parseError as Error;
        continue;
      }

      // Validate the response structure using Zod
      const validationResult = TeamSelectionResponseSchema.safeParse(parsedResponse);

      if (!validationResult.success) {
        const validationErrors = validationResult.error.issues.map(issue => 
          `${issue.path.join('.')}: ${issue.message}`
        ).join(', ');
        
        console.error(`Test Team Selection: Validation failed (attempt ${attempt}):`, validationErrors);
        console.error(`Test Team Selection: Invalid response structure:`, parsedResponse);
        
        if (attempt === maxRetries) {
          throw new Error(`Response validation failed after ${maxRetries} attempts. Validation errors: ${validationErrors}`);
        }
        
        // Continue to next attempt
        lastError = new Error(`Validation failed: ${validationErrors}`);
        continue;
      }

      const { selectedTeam, rationale, confidence, reasoning } = validationResult.data;

      // Additional validation: Ensure the selected team exists in our enum
      if (!Object.values(AgenticTeamId).includes(selectedTeam as AgenticTeamId)) {
        console.error(`Test Team Selection: Invalid team ID selected: ${selectedTeam}`);
        
        if (attempt === maxRetries) {
          throw new Error(`Invalid team ID selected: ${selectedTeam}. Valid options: ${Object.values(AgenticTeamId).join(', ')}`);
        }
        
        lastError = new Error(`Invalid team ID: ${selectedTeam}`);
        continue;
      }

      // Success! Return the validated result
      const teamId = selectedTeam as AgenticTeamId;
      const teamName = TEAM_DESCRIPTIONS[selectedTeam]?.name || 'Unknown Team';

      console.log(`Test Team Selection: Successfully selected ${teamId} (${teamName}) with confidence ${confidence}`);
      console.log(`Test Team Selection: Rationale: ${rationale}`);

      return {
        selectedTeams: [teamId],
        rationale: rationale,
        confidence: confidence,
        reasoning: reasoning
      };

    } catch (error) {
      console.error(`Test Team Selection: Error in attempt ${attempt}:`, error);
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        console.error(`Test Team Selection: All ${maxRetries} attempts failed. Last error:`, lastError);
        throw lastError;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }

  // This should never be reached, but just in case
  throw lastError || new Error('Team selection failed for unknown reasons');
}

export async function POST(req: NextRequest) {
  try {
    console.log('Test Team Selection API: Starting request processing');
    
    // Parse request body
    const body = await req.json();
    const { title, description, priority, category, requirements, expectedOutcome } = body;

    // Validate request body
    if (!description) {
      return NextResponse.json({ error: 'Description is required' }, { status: 400 });
    }

    console.log('Test Team Selection API: Processing request for:', { title, description: description.substring(0, 100) + '...' });

    // Perform team selection
    const teamSelectionResult = await selectTeamWithLLM(
      description,
      title,
      priority,
      category,
      requirements,
      expectedOutcome
    );

    const response = {
      selectedTeams: teamSelectionResult.selectedTeams,
      teamSelectionRationale: teamSelectionResult.rationale,
      teamSelectionConfidence: teamSelectionResult.confidence,
      teamSelectionReasoning: teamSelectionResult.reasoning || null,
      selectedTeamDetails: teamSelectionResult.selectedTeams.map(teamId => ({
        id: teamId,
        name: TEAM_DESCRIPTIONS[teamId]?.name || 'Unknown',
        expertise: TEAM_DESCRIPTIONS[teamId]?.expertise || 'Unknown'
      }))
    };

    console.log('Test Team Selection API: Returning response:', response);

    return NextResponse.json(response);
    
  } catch (error: any) {
    console.error('Test Team Selection API: Error:', error);
    
    return NextResponse.json({
      error: error.message || 'Failed to perform team selection',
      details: process.env.NODE_ENV === 'development' ? {
        stack: error.stack,
        timestamp: new Date().toISOString()
      } : undefined
    }, { status: 500 });
  }
}

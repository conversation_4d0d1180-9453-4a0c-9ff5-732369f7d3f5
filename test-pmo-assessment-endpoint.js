/**
 * Simple test to verify the PMO assessment endpoint is working
 */

const testCases = [
  {
    title: "Marketing Campaign Development",
    description: "Develop a comprehensive digital marketing campaign for our new product launch, including social media strategy, content creation, and brand positioning.",
    expectedTeam: "Marketing"
  },
  {
    title: "Software Application Development", 
    description: "Build a mobile application for customer service management with real-time chat, ticket tracking, and analytics dashboard.",
    expectedTeam: "SoftwareDesign"
  },
  {
    title: "Market Research Analysis",
    description: "Conduct comprehensive market research to analyze competitor strategies, customer preferences, and market trends in the fintech industry.",
    expectedTeam: "Research"
  }
];

async function testPMOAssessmentEndpoint() {
  console.log('🧪 Testing PMO Assessment Endpoint');
  console.log('=====================================\n');

  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.title}`);
      console.log(`📝 Description: ${testCase.description}`);
      console.log(`🎯 Expected Team: ${testCase.expectedTeam}`);
      
      const requestBody = {
        formData: {
          title: testCase.title,
          description: testCase.description,
          priority: 'Medium',
          category: 'Test',
          requirements: 'Standard requirements',
          expectedOutcome: 'Successful completion'
        },
        userId: '<EMAIL>'
      };

      console.log('📤 Sending request to /api/test-team-selection...');
      
      const response = await fetch('http://localhost:3000/api/test-team-selection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          title: testCase.title,
          description: testCase.description,
          priority: 'Medium',
          category: 'Test'
        })
      });
      
      console.log(`📥 Response status: ${response.status} ${response.statusText}`);
      
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      console.log(`📄 Content-Type: ${contentType}`);
      
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.log('❌ Non-JSON response received:');
        console.log(textResponse.substring(0, 500) + '...');
        console.log('\n');
        continue;
      }
      
      const result = await response.json();
      
      if (response.ok && result.selectedTeams) {
        const selectedTeams = result.selectedTeams || [];
        const teamNames = selectedTeams.map(teamId => {
          const teamMapping = {
            'Ag001': 'Marketing',
            'Ag002': 'Research', 
            'Ag003': 'SoftwareDesign',
            'Ag004': 'Sales',
            'Ag005': 'BusinessAnalysis',
            'Ag006': 'InvestigativeResearch'
          };
          return teamMapping[teamId] || teamId;
        });
        
        const isCorrect = teamNames.includes(testCase.expectedTeam);
        const status = isCorrect ? "✅ PASS" : "❌ FAIL";
        
        console.log(`🤖 Selected Team: ${teamNames.join(', ')}`);
        console.log(`💭 Rationale: ${result.teamSelectionRationale || 'No rationale provided'}`);
        console.log(`🎯 Confidence: ${result.teamSelectionConfidence || 'Unknown'}`);
        console.log(`${status}\n`);
        
        if (result.isEmergencyFallback) {
          console.log(`⚠️ Emergency Fallback Used: ${result.isEmergencyFallback}`);
        }
        
      } else {
        console.log(`❌ API Error: ${result.error || 'Unknown error'}`);
        if (result.details) {
          console.log(`📋 Details: ${JSON.stringify(result.details, null, 2)}`);
        }
        console.log('\n');
      }
      
    } catch (error) {
      console.error(`💥 Test failed with error: ${error.message}`);
      console.log('\n');
    }
  }
}

// Run the test
testPMOAssessmentEndpoint().catch(console.error);

/**
 * Unit test for team selection functions
 */

// Mock the Groq AI function for testing
const mockProcessWithGroq = async (options) => {
  const { prompt } = options;

  // Extract the description from the prompt
  const descriptionMatch = prompt.match(/Given the following PMO request assessment: (.+?)\n/);
  const description = descriptionMatch ? descriptionMatch[1].toLowerCase() : prompt.toLowerCase();

  // Simple mock responses based on keywords in the description
  if (description.includes('marketing') || description.includes('brand') || description.includes('campaign')) {
    return JSON.stringify({
      selectedTeam: "Ag001",
      rationale: "Marketing-related content detected"
    });
  } else if (description.includes('software') || description.includes('develop') || description.includes('app') || description.includes('ui/ux') || description.includes('technical')) {
    return JSON.stringify({
      selectedTeam: "Ag003",
      rationale: "Software development content detected"
    });
  } else if (description.includes('sales') || description.includes('revenue') || description.includes('client')) {
    return JSON.stringify({
      selectedTeam: "Ag004",
      rationale: "Sales-related content detected"
    });
  } else if (description.includes('business') || description.includes('process') || description.includes('requirements')) {
    return JSON.stringify({
      selectedTeam: "Ag005",
      rationale: "Business analysis content detected"
    });
  } else if (description.includes('research') || description.includes('analysis') || description.includes('data')) {
    return JSON.stringify({
      selectedTeam: "Ag002",
      rationale: "Research-related content detected"
    });
  } else {
    return JSON.stringify({
      selectedTeam: "Ag002",
      rationale: "Default research team selection"
    });
  }
};

// Mock the AgenticTeamId enum
const AgenticTeamId = {
  Marketing: 'Ag001',
  Research: 'Ag002',
  SoftwareDesign: 'Ag003',
  Sales: 'Ag004',
  BusinessAnalysis: 'Ag005',
  InvestigativeResearch: 'Ag006'
};

// Mock Zod schema
const TeamSelectionResponseSchema = {
  safeParse: (data) => {
    const validTeams = ['Ag001', 'Ag002', 'Ag003', 'Ag004', 'Ag005', 'Ag006'];
    if (data && data.selectedTeam && validTeams.includes(data.selectedTeam)) {
      return { success: true, data };
    }
    return { success: false, error: 'Invalid team selection' };
  }
};

// Copy the team selection functions from our implementation
async function selectTeamWithLLM(description) {
  try {
    const prompt = `Given the following PMO request assessment: ${description}

Your task is to review the assessment and determine which Agent team is best suited to process this task. Choose from these available teams:

- Ag001 (Marketing Team): Specializes in marketing strategy, content creation, brand management, and market analysis
- Ag002 (Research Team): Focuses on data collection, analysis, literature reviews, and producing research reports
- Ag003 (Software Design Team): Handles software development, UI/UX design, coding, and technical implementation
- Ag004 (Sales Team): Manages sales strategies, client relationships, proposal development, and revenue generation
- Ag005 (Business Analysis Team): Specializes in strategic planning, process analysis, requirements engineering, and software engineering documentation workflows
- Ag006 (Investigative Research Team): Conducts comprehensive investigative research using specialized journalist AI agents and multi-LLM analysis

Respond with only the team identifier (e.g., "Ag002") that best matches the requirements described in the assessment.

Format your response as JSON:
{
  "selectedTeam": "Ag002",
  "rationale": "Brief explanation of why this team is most suitable"
}`;

    const response = await mockProcessWithGroq({
      prompt,
      model: 'deepseek-r1-distill-llama-70b',
      modelOptions: {
        temperature: 0.3,
        max_tokens: 500
      }
    });

    let parsedResponse;
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : response;
      parsedResponse = JSON.parse(jsonStr);
    } catch (parseError) {
      console.warn('Failed to parse LLM response as JSON, falling back to keyword matching:', parseError);
      throw new Error('Invalid JSON response from LLM');
    }

    const validationResult = TeamSelectionResponseSchema.safeParse(parsedResponse);
    
    if (!validationResult.success) {
      console.warn('LLM response validation failed:', validationResult.error);
      throw new Error('Invalid response format from LLM');
    }

    const { selectedTeam, rationale } = validationResult.data;
    const teamId = selectedTeam;
    
    return {
      selectedTeams: [teamId],
      rationale: rationale || 'Selected by LLM analysis'
    };

  } catch (error) {
    console.error('Error in LLM team selection:', error);
    return selectTeamWithKeywords(description);
  }
}

function selectTeamWithKeywords(description) {
  const lowerDescription = description.toLowerCase();
  
  if (lowerDescription.includes('market') || lowerDescription.includes('brand') || lowerDescription.includes('campaign')) {
    return {
      selectedTeams: [AgenticTeamId.Marketing],
      rationale: 'Selected based on marketing-related keywords'
    };
  } else if (lowerDescription.includes('research') || lowerDescription.includes('analysis') || lowerDescription.includes('data')) {
    return {
      selectedTeams: [AgenticTeamId.Research],
      rationale: 'Selected based on research-related keywords'
    };
  } else if (lowerDescription.includes('software') || lowerDescription.includes('develop') || lowerDescription.includes('code') || lowerDescription.includes('app')) {
    return {
      selectedTeams: [AgenticTeamId.SoftwareDesign],
      rationale: 'Selected based on software development keywords'
    };
  } else if (lowerDescription.includes('sales') || lowerDescription.includes('client') || lowerDescription.includes('revenue')) {
    return {
      selectedTeams: [AgenticTeamId.Sales],
      rationale: 'Selected based on sales-related keywords'
    };
  } else if (lowerDescription.includes('business') || lowerDescription.includes('process') || lowerDescription.includes('strategy')) {
    return {
      selectedTeams: [AgenticTeamId.BusinessAnalysis],
      rationale: 'Selected based on business analysis keywords'
    };
  } else {
    return {
      selectedTeams: [AgenticTeamId.Research],
      rationale: 'Default selection when no specific keywords match'
    };
  }
}

// Test cases
const testCases = [
  {
    description: "Develop a comprehensive marketing campaign for our new SaaS product",
    expectedTeam: "Ag001"
  },
  {
    description: "Design and develop a mobile application with modern UI/UX",
    expectedTeam: "Ag003"
  },
  {
    description: "Improve sales strategies and client relationships",
    expectedTeam: "Ag004"
  },
  {
    description: "Analyze business processes and gather requirements",
    expectedTeam: "Ag005"
  },
  {
    description: "Conduct market research and data analysis",
    expectedTeam: "Ag002"
  }
];

async function runUnitTests() {
  console.log("🧪 Running Unit Tests for Team Selection Functions\n");
  
  for (const testCase of testCases) {
    try {
      console.log(`📝 Testing: "${testCase.description}"`);
      console.log(`🎯 Expected: ${testCase.expectedTeam}`);
      
      const result = await selectTeamWithLLM(testCase.description);
      const selectedTeam = result.selectedTeams[0];
      
      const isCorrect = selectedTeam === testCase.expectedTeam;
      const status = isCorrect ? "✅ PASS" : "❌ FAIL";
      
      console.log(`🤖 Selected: ${selectedTeam}`);
      console.log(`💭 Rationale: ${result.rationale}`);
      console.log(`${status}\n`);
      
    } catch (error) {
      console.log(`❌ Test Error: ${error.message}\n`);
    }
  }
}

// Run tests
if (typeof window === 'undefined') {
  runUnitTests().catch(console.error);
}

module.exports = { selectTeamWithLLM, selectTeamWithKeywords, testCases, runUnitTests };

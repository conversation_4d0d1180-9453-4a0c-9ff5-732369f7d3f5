import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { PMOAssessmentAgent } from '../../../lib/agents/pmo/PMOAssessmentAgent';
import { PMOFormInput, ModelProvider, AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';
import { QueryDocumentsAgent } from '../../../components/Agents/QueryDocumentsAgent';
import { createLlmService } from '../../../lib/tools/llmServiceAdapter';
import { getStorage, ref, getDownloadURL } from 'firebase/storage';
import { doc, getDoc, collection, query, where, getDocs, limit } from 'firebase/firestore';
import { db } from '../../../components/firebase';
import { processWithGroq } from '../../../lib/tools/groq-ai';
import { z } from 'zod';

// Enhanced Zod schema for LLM team selection response with strict validation
const TeamSelectionResponseSchema = z.object({
  selectedTeam: z.enum(['Ag001', 'Ag002', 'Ag003', 'Ag004', 'Ag005', 'Ag006']),
  rationale: z.string().min(20, 'Rationale must be at least 20 characters long'),
  confidence: z.number().min(0).max(1).default(0.8),
  reasoning: z.string().min(10, 'Reasoning must be at least 10 characters long').optional()
});

// Team information for comprehensive LLM context
const TEAM_DESCRIPTIONS = {
  'Ag001': {
    name: 'Marketing Team',
    expertise: 'Marketing strategy, content creation, brand management, market research, campaigns, customer acquisition, digital marketing, SEO, social media, advertising, promotion, lead generation, brand awareness',
    deliverables: 'Marketing plans, content strategies, campaign designs, brand guidelines, market research reports, customer acquisition strategies'
  },
  'Ag002': {
    name: 'Research Team',
    expertise: 'Data collection, analysis, literature reviews, research reports, surveys, statistical analysis, market research, competitive analysis, user research, academic research, data insights',
    deliverables: 'Research reports, data analysis, survey results, literature reviews, market studies, statistical findings, research methodologies'
  },
  'Ag003': {
    name: 'Software Design Team',
    expertise: 'Software development, UI/UX design, coding, technical implementation, mobile applications, web applications, system architecture, database design, API development, frontend, backend',
    deliverables: 'Software applications, technical specifications, system designs, code implementations, user interfaces, technical documentation'
  },
  'Ag004': {
    name: 'Sales Team',
    expertise: 'Sales strategies, client relationships, proposal development, revenue generation, CRM management, business development, lead conversion, account management, negotiation, sales processes',
    deliverables: 'Sales strategies, client proposals, revenue plans, CRM implementations, sales processes, account management systems'
  },
  'Ag005': {
    name: 'Business Analysis Team',
    expertise: 'Strategic planning, process analysis, requirements engineering, workflow optimization, business consulting, operational efficiency, business transformation, process improvement, strategic analysis',
    deliverables: 'Business requirements, process maps, strategic plans, workflow optimizations, business cases, operational improvements'
  },
  'Ag006': {
    name: 'Investigative Research Team',
    expertise: 'Comprehensive investigative research, editorial content, multi-source analysis, fact-checking, investigative journalism, deep research, cross-reference validation, complex research projects',
    deliverables: 'Investigative reports, editorial content, comprehensive research studies, fact-checked analyses, multi-source investigations'
  }
};

/**
 * Enhanced LLM-powered team selection with comprehensive error handling and retry logic
 */
async function selectTeamWithLLM(
  description: string, 
  projectTitle: string = '', 
  priority: string = 'Medium',
  category: string = 'Unknown',
  requirements: string = '',
  expectedOutcome: string = ''
): Promise<{
  selectedTeams: AgenticTeamId[];
  rationale: string;
  confidence: number;
  reasoning?: string;
}> {
  
  // Validate input parameters
  if (!description || description.trim().length < 5) {
    throw new Error('Project description is too short or empty. Please provide a meaningful description for accurate team selection.');
  }

  // Construct comprehensive project context
  const projectContext = `
PROJECT TITLE: ${projectTitle || 'Not specified'}
DESCRIPTION: ${description}
PRIORITY: ${priority}
CATEGORY: ${category}
REQUIREMENTS: ${requirements || 'Not specified'}
EXPECTED OUTCOME: ${expectedOutcome || 'Not specified'}
`.trim();

  // Enhanced prompt with comprehensive team information and selection criteria
  const prompt = `You are an expert PMO (Project Management Office) team assignment specialist with deep knowledge of organizational capabilities and project requirements. Your task is to analyze the project details and select the single most appropriate team.

PROJECT DETAILS:
${projectContext}

AVAILABLE TEAMS WITH DETAILED CAPABILITIES:

${Object.entries(TEAM_DESCRIPTIONS).map(([id, info]) => `
${id} (${info.name}):
• Expertise: ${info.expertise}
• Typical Deliverables: ${info.deliverables}
`).join('\n')}

SELECTION METHODOLOGY:
1. Analyze the primary objective and scope of the project
2. Identify the core skills and expertise required
3. Match project requirements to team capabilities
4. Consider the expected deliverables and outcomes
5. Evaluate which team can provide the highest value and success probability

CRITICAL REQUIREMENTS:
- Select EXACTLY ONE team that best matches the project requirements
- Provide detailed rationale based on specific project needs
- Assign a confidence score (0.0 to 1.0) based on the match quality
- Consider both primary and secondary project aspects

RESPONSE FORMAT:
You must respond with ONLY valid JSON in this exact structure:
{
  "selectedTeam": "Ag00X",
  "rationale": "Comprehensive explanation of why this specific team was selected, referencing specific project requirements and team capabilities",
  "confidence": 0.95,
  "reasoning": "Step-by-step analysis of the selection process and key factors that influenced the decision"
}

EXAMPLES OF GOOD RATIONALE:
- "Selected Ag003 (Software Design Team) because the project requires developing a mobile application with real-time tracking capabilities, which directly aligns with their expertise in mobile development, database design, and system architecture."
- "Chose Ag001 (Marketing Team) as the project focuses on creating a comprehensive digital marketing campaign for product launch, requiring their specialized skills in campaign design, brand management, and customer acquisition strategies."

Analyze the project thoroughly and provide your selection:`;

  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`PMO Team Selection: Attempt ${attempt}/${maxRetries} - Calling Groq AI`);
      console.log(`PMO Team Selection: Project context length: ${projectContext.length} characters`);

      // Call Groq AI with optimized parameters for consistent results
      const response = await processWithGroq({
        prompt,
        model: 'deepseek-r1-distill-llama-70b',
        modelOptions: {
          temperature: 0.1, // Very low temperature for consistent results
          max_tokens: 800,
          top_p: 0.9,
          frequency_penalty: 0.0,
          presence_penalty: 0.0
        }
      });

      console.log(`PMO Team Selection: Raw LLM response (attempt ${attempt}):`, response);

      // Enhanced JSON extraction with multiple parsing strategies
      let parsedResponse;
      try {
        // Strategy 1: Clean and extract JSON directly
        const cleanResponse = response
          .replace(/```json\s*/gi, '')
          .replace(/```\s*/g, '')
          .replace(/^[^{]*/, '')
          .replace(/}[^}]*$/, '}')
          .trim();

        // Strategy 2: Use regex to find JSON object
        const jsonRegex = /\{[\s\S]*?\}/;
        const jsonMatch = cleanResponse.match(jsonRegex);
        
        if (jsonMatch) {
          parsedResponse = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON object found in response');
        }

        console.log(`PMO Team Selection: Successfully parsed JSON (attempt ${attempt}):`, parsedResponse);
      } catch (parseError) {
        console.error(`PMO Team Selection: JSON parsing failed (attempt ${attempt}):`, parseError);
        console.error(`PMO Team Selection: Response was:`, response);
        
        if (attempt === maxRetries) {
          const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
          throw new Error(`Failed to parse LLM response as JSON after ${maxRetries} attempts. Last error: ${errorMessage}`);
        }
        
        // Continue to next attempt
        lastError = parseError as Error;
        continue;
      }

      // Validate the response structure using Zod
      const validationResult = TeamSelectionResponseSchema.safeParse(parsedResponse);

      if (!validationResult.success) {
        const validationErrors = validationResult.error.issues.map(issue => 
          `${issue.path.join('.')}: ${issue.message}`
        ).join(', ');
        
        console.error(`PMO Team Selection: Validation failed (attempt ${attempt}):`, validationErrors);
        console.error(`PMO Team Selection: Invalid response structure:`, parsedResponse);
        
        if (attempt === maxRetries) {
          throw new Error(`Response validation failed after ${maxRetries} attempts. Validation errors: ${validationErrors}`);
        }
        
        // Continue to next attempt
        lastError = new Error(`Validation failed: ${validationErrors}`);
        continue;
      }

      const { selectedTeam, rationale, confidence, reasoning } = validationResult.data;

      // Additional validation: Ensure the selected team exists in our enum
      if (!Object.values(AgenticTeamId).includes(selectedTeam as AgenticTeamId)) {
        console.error(`PMO Team Selection: Invalid team ID selected: ${selectedTeam}`);
        
        if (attempt === maxRetries) {
          throw new Error(`Invalid team ID selected: ${selectedTeam}. Valid options: ${Object.values(AgenticTeamId).join(', ')}`);
        }
        
        lastError = new Error(`Invalid team ID: ${selectedTeam}`);
        continue;
      }

      // Success! Return the validated result
      const teamId = selectedTeam as AgenticTeamId;
      const teamName = TEAM_DESCRIPTIONS[selectedTeam]?.name || 'Unknown Team';

      console.log(`PMO Team Selection: Successfully selected ${teamId} (${teamName}) with confidence ${confidence}`);
      console.log(`PMO Team Selection: Rationale: ${rationale}`);

      return {
        selectedTeams: [teamId],
        rationale: rationale,
        confidence: confidence,
        reasoning: reasoning
      };

    } catch (error) {
      console.error(`PMO Team Selection: Error in attempt ${attempt}:`, error);
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        // All attempts failed
        break;
      }
      
      // Wait before retrying (exponential backoff)
      const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
      console.log(`PMO Team Selection: Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // If we reach here, all attempts failed
  const errorMessage = `LLM team selection failed after ${maxRetries} attempts. Last error: ${lastError?.message || 'Unknown error'}`;
  console.error('PMO Team Selection: All attempts failed:', errorMessage);
  
  throw new Error(errorMessage);
}

/**
 * Intelligent emergency fallback when LLM completely fails
 * This should only be used as a last resort and should log the failure prominently
 */
function getEmergencyFallback(description: string, projectTitle: string = ''): {
  selectedTeams: AgenticTeamId[];
  rationale: string;
  confidence: number;
} {
  console.error('PMO Team Selection: EMERGENCY FALLBACK ACTIVATED - LLM selection completely failed');
  console.error('PMO Team Selection: This indicates a serious system issue that needs immediate attention');
  
  // Simple heuristic based on description length and common patterns
  const combinedText = `${projectTitle} ${description}`.toLowerCase();
  
  // Very basic pattern matching for emergency situations only
  let emergencyTeam: AgenticTeamId;
  let emergencyReason: string;
  
  if (combinedText.includes('software') || combinedText.includes('app') || combinedText.includes('develop')) {
    emergencyTeam = AgenticTeamId.SoftwareDesign;
    emergencyReason = 'Emergency selection based on software-related terms';
  } else if (combinedText.includes('market') || combinedText.includes('campaign') || combinedText.includes('brand')) {
    emergencyTeam = AgenticTeamId.Marketing;
    emergencyReason = 'Emergency selection based on marketing-related terms';
  } else if (combinedText.includes('sales') || combinedText.includes('client') || combinedText.includes('revenue')) {
    emergencyTeam = AgenticTeamId.Sales;
    emergencyReason = 'Emergency selection based on sales-related terms';
  } else if (combinedText.includes('research') || combinedText.includes('analysis') || combinedText.includes('study')) {
    emergencyTeam = AgenticTeamId.Research;
    emergencyReason = 'Emergency selection based on research-related terms';
  } else {
    // Ultimate fallback - Business Analysis as the most general team
    emergencyTeam = AgenticTeamId.BusinessAnalysis;
    emergencyReason = 'Emergency fallback selection - Business Analysis chosen as most general team';
  }
  
  return {
    selectedTeams: [emergencyTeam],
    rationale: `EMERGENCY FALLBACK: ${emergencyReason}. LLM selection failed completely. This requires immediate system investigation.`,
    confidence: 0.1 // Very low confidence to indicate this is an emergency fallback
  };
}

export async function POST(req: NextRequest) {
  let body: any = null;

  try {
    console.log('PMO Assessment API: Starting request processing');

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      console.log('PMO Assessment API: Authentication failed');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('PMO Assessment API: Authentication successful for user:', session.user.email);

    // Parse request body
    try {
      body = await req.json();
      console.log('PMO Assessment API: Request body parsed successfully');
    } catch (parseError) {
      console.error('PMO Assessment API: Failed to parse request body:', parseError);
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    const { formData, userId } = body;
    console.log('PMO Assessment API: Extracted formData and userId from request');

    // Validate request body
    if (!formData || !userId) {
      console.error('PMO Assessment API: Missing required fields - formData:', !!formData, 'userId:', !!userId);
      return NextResponse.json({ error: 'Missing required fields: formData and userId are required' }, { status: 400 });
    }

    // Validate that the authenticated user matches the requested userId
    if (session.user.email !== userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create the PMO assessment agent
    console.log('PMO Assessment API: Creating PMO assessment agent');

    const modelProvider = formData.modelProvider || 'openai';
    const validModelProvider = (modelProvider && ['openai', 'anthropic', 'groq', 'google'].includes(modelProvider))
      ? modelProvider as ModelProvider
      : 'openai';

    console.log('PMO Assessment API: Using model provider:', validModelProvider);

    let llmService, pmoAgent, queryDocumentsAgent;

    try {
      llmService = createLlmService(validModelProvider);
      console.log('PMO Assessment API: LLM service created successfully');

      pmoAgent = new PMOAssessmentAgent(
        {
          userId,
          includeExplanation: false,
          streamResponse: false
        },
        llmService
      );
      console.log('PMO Assessment API: PMO agent created successfully');

      // Create and set the query documents agent
      queryDocumentsAgent = new QueryDocumentsAgent({
        userId,
        includeExplanation: false
      });
      console.log('PMO Assessment API: Query documents agent created successfully');

      pmoAgent.setQueryDocumentsAgent(queryDocumentsAgent);
      console.log('PMO Assessment API: Query documents agent set on PMO agent');
    } catch (agentError) {
      console.error('PMO Assessment API: Failed to create agents:', agentError);
      return NextResponse.json({
        error: 'Failed to initialize assessment agents',
        details: agentError instanceof Error ? agentError.message : 'Unknown agent initialization error'
      }, { status: 500 });
    }

    // Context data processing (existing logic remains the same)
    let contextData = '';
    let contextChunks: any[] = [];

    // Get context data from the form input
    if (formData.contextOptions) {
      if (formData.contextOptions.customContext) {
        contextData = formData.contextOptions.customContext;
        contextChunks = [{
          content: formData.contextOptions.customContext,
          metadata: {
            fileName: 'Custom Context',
            source: 'User Input',
            relevance: 1.0
          },
          text: formData.contextOptions.customContext
        }];
        console.log(`PMO Assessment API: Created synthetic chunk for custom context`);
      } else if (formData.contextOptions.fileIds && formData.contextOptions.fileIds.length > 0) {
        try {
          console.log(`PMO Assessment API: Directly fetching document content for file ID: ${formData.contextOptions.fileIds[0]}`);

          const fileId = formData.contextOptions.fileIds[0];
          const fileRef = doc(db, `users/${userId}/files`, fileId);
          const fileDoc = await getDoc(fileRef);

          if (fileDoc.exists()) {
            const fileData = fileDoc.data();
            console.log(`PMO Assessment API: Found file: ${fileData.name}`);

            let documentContent = '';

            if (fileData.contents) {
              documentContent = fileData.contents;
              console.log(`PMO Assessment API: Using content stored in Firestore`);
            } else if (fileData.textFilePath) {
              console.log(`PMO Assessment API: Fetching content from Storage path: ${fileData.textFilePath}`);
              const storage = getStorage();
              const textFileRef = ref(storage, fileData.textFilePath);
              const textFileUrl = await getDownloadURL(textFileRef);

              const response = await fetch(textFileUrl);
              documentContent = await response.text();
            } else if (fileData.downloadUrl) {
              console.log(`PMO Assessment API: No extracted text available, using file metadata as context`);
              documentContent = `File: ${fileData.name}\nType: ${fileData.type}\nCategory: ${fileData.category}\nDownload URL: ${fileData.downloadUrl}`;
            }

            contextData = documentContent;

            const chunkSize = 2000;
            const chunks = [];

            for (let i = 0; i < documentContent.length; i += chunkSize) {
              const chunkContent = documentContent.substring(i, i + chunkSize);
              chunks.push({
                content: chunkContent,
                metadata: {
                  fileName: fileData.name,
                  source: fileData.category || 'Unknown',
                  relevance: 1.0 - (i / documentContent.length * 0.5)
                },
                text: chunkContent
              });
            }

            contextChunks = chunks;
            console.log(`PMO Assessment API: Created ${chunks.length} chunks from document content`);
          } else {
            console.error(`PMO Assessment API: File not found: ${fileId}`);
            contextData = `Error: File with ID ${fileId} not found`;
            contextChunks = [{
              content: contextData,
              metadata: {
                fileName: 'Error',
                source: 'Error',
                relevance: 0.5
              },
              text: contextData
            }];
          }
        } catch (error) {
          console.error(`PMO Assessment API: Error fetching document:`, error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          contextData = `Error fetching document: ${errorMessage}`;
          contextChunks = [{
            content: contextData,
            metadata: {
              fileName: 'Error',
              source: 'Error',
              relevance: 0.5
            },
            text: contextData
          }];
        }
      } else if (formData.contextOptions.categoryIds && formData.contextOptions.categoryIds.length > 0) {
        try {
          const categoryId = formData.contextOptions.categoryIds[0];
          console.log(`PMO Assessment API: Directly fetching files for category: ${categoryId}`);

          const filesRef = collection(db, `users/${userId}/files`);
          const q = query(filesRef, where('category', '==', categoryId), limit(5));
          const querySnapshot = await getDocs(q);

          if (!querySnapshot.empty) {
            console.log(`PMO Assessment API: Found ${querySnapshot.size} files in category ${categoryId}`);

            const allChunks = [];
            let combinedContent = '';

            for (const doc of querySnapshot.docs) {
              const fileData = doc.data();
              console.log(`PMO Assessment API: Processing file: ${fileData.name}`);

              let documentContent = '';

              if (fileData.contents) {
                documentContent = fileData.contents;
              } else if (fileData.textFilePath) {
                documentContent = `File: ${fileData.name}\nType: ${fileData.type}\nCategory: ${fileData.category}`;
              } else if (fileData.downloadUrl) {
                documentContent = `File: ${fileData.name}\nType: ${fileData.type}\nCategory: ${fileData.category}`;
              }

              combinedContent += `\n\n--- FILE: ${fileData.name} ---\n\n${documentContent}`;

              allChunks.push({
                content: documentContent,
                metadata: {
                  fileName: fileData.name,
                  source: fileData.category || categoryId,
                  relevance: 0.9
                },
                text: documentContent
              });
            }

            contextData = combinedContent.trim();
            contextChunks = allChunks;
            console.log(`PMO Assessment API: Created ${allChunks.length} chunks from category files`);
          } else {
            console.log(`PMO Assessment API: No files found in category ${categoryId}`);
            contextData = `No files found in category ${categoryId}`;
            contextChunks = [{
              content: contextData,
              metadata: {
                fileName: 'No Files Found',
                source: categoryId,
                relevance: 0.5
              },
              text: contextData
            }];
          }
        } catch (error) {
          console.error(`PMO Assessment API: Error fetching category files:`, error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          contextData = `Error fetching category files: ${errorMessage}`;
          contextChunks = [{
            content: contextData,
            metadata: {
              fileName: 'Error',
              source: 'Error',
              relevance: 0.5
            },
            text: contextData
          }];
        }
      }
    }

    // Ensure we always have at least one context chunk
    if (contextChunks.length === 0) {
      console.log(`PMO Assessment API: No context chunks created, adding a default chunk`);
      const defaultContent = formData.description;
      contextChunks = [{
        content: defaultContent,
        metadata: {
          fileName: 'Project Description',
          source: 'User Input',
          relevance: 1.0
        },
        text: defaultContent
      }];
      contextData = defaultContent;
    }

    // Log the context chunks for debugging
    console.log(`PMO Assessment API: Final context chunks count: ${contextChunks.length}`);
    if (contextChunks.length > 0) {
      contextChunks.forEach((chunk, index) => {
        const source = chunk.metadata?.fileName || chunk.metadata?.source || 'N/A';
        const relevance = chunk.metadata?.relevance ? ` (Relevance: ${(chunk.metadata.relevance * 100).toFixed(1)}%)` : '';
        console.log(`PMO Assessment API: Context Chunk ${index + 1} - [Section: ${source}${relevance}]`);
        console.log(`PMO Assessment API: Content preview: ${(chunk.content || chunk.text || 'No content').substring(0, 100)}...`);
      });
    }

    // Perform LLM-powered team selection with comprehensive project information
    console.log('PMO Assessment API: Performing LLM-powered team selection');
    console.log('PMO Assessment API: Project details:', {
      title: formData.title,
      description: formData.description?.substring(0, 100) + '...',
      priority: formData.priority,
      category: formData.category
    });

    let teamSelectionResult;
    try {
      teamSelectionResult = await selectTeamWithLLM(
        formData.description,
        formData.title,
        formData.priority,
        formData.category,
        formData.requirements,
        formData.expectedOutcome
      );
      console.log('PMO Assessment API: Team selection completed successfully');
    } catch (error) {
      console.error('PMO Assessment API: LLM team selection failed completely:', error);

      // Only use emergency fallback if LLM completely fails
      // This should be extremely rare and indicates a serious system issue
      teamSelectionResult = getEmergencyFallback(formData.description, formData.title);

      // Log this as a critical error that needs immediate attention
      console.error('PMO Assessment API: CRITICAL ERROR - Emergency fallback activated');
      console.error('PMO Assessment API: This indicates LLM service failure and requires immediate investigation');
    }

    const selectedTeams = teamSelectionResult.selectedTeams;
    const isEmergencyFallback = teamSelectionResult.confidence < 0.5;

    console.log(`PMO Assessment API: Selected teams: ${selectedTeams.join(', ')}`);
    console.log(`PMO Assessment API: Selection rationale: ${teamSelectionResult.rationale}`);
    console.log(`PMO Assessment API: Selection confidence: ${teamSelectionResult.confidence}`);
    console.log(`PMO Assessment API: Emergency fallback used: ${isEmergencyFallback}`);

    // Create enhanced assessment for requirements specification
    const enhancedAssessment = `
Project Title: ${formData.title}
Description: ${formData.description}
Priority: ${formData.priority || 'Medium'}
Category: ${formData.category || 'Unknown'}
Requirements: ${formData.requirements || 'Not specified'}
Expected Outcome: ${formData.expectedOutcome || 'Not specified'}
Context: ${contextData ? 'Additional context provided' : 'No additional context'}
Selected Team: ${selectedTeams.join(', ')}
Team Selection Rationale: ${teamSelectionResult.rationale}
`.trim();

    // Generate the requirements specification
    const requirementsSpec = await pmoAgent.generateRequirementsSpecificationDirectly(
      formData as PMOFormInput,
      enhancedAssessment,
      selectedTeams,
      contextChunks
    );

    // Return comprehensive response with team selection details
    const response = {
      pmoAssessment: requirementsSpec,
      selectedTeams: selectedTeams,
      teamSelectionRationale: teamSelectionResult.rationale,
      teamSelectionConfidence: teamSelectionResult.confidence,
      teamSelectionReasoning: (teamSelectionResult as any).reasoning || null,
      isEmergencyFallback: isEmergencyFallback,
      debugInfo: {
        assessmentInput: enhancedAssessment,
        contextChunksCount: contextChunks.length,
        selectionMethod: isEmergencyFallback ? 'Emergency Fallback' : 'LLM',
        selectedTeamDetails: selectedTeams.map(teamId => ({
          id: teamId,
          name: TEAM_DESCRIPTIONS[teamId]?.name || 'Unknown',
          expertise: TEAM_DESCRIPTIONS[teamId]?.expertise || 'Unknown'
        })),
        ...(isEmergencyFallback && {
          warning: 'Emergency fallback was used due to LLM failure. This indicates a serious system issue that requires immediate attention.'
        })
      }
    };

    return NextResponse.json(response);
    
  } catch (error: any) {
    console.error('PMO Assessment API: Critical error in main handler:', error);
    
    // Provide detailed error information for debugging
    const errorResponse = {
      error: error.message || 'Failed to generate PMO assessment',
      details: process.env.NODE_ENV === 'development' ? {
        stack: error.stack,
        timestamp: new Date().toISOString(),
        requestData: {
          userId: body?.userId,
          hasFormData: !!body?.formData,
          formDataKeys: body?.formData ? Object.keys(body.formData) : []
        }
      } : {
        timestamp: new Date().toISOString(),
        message: 'Internal server error - check logs for details'
      }
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}
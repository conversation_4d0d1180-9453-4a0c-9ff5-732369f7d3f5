/**
 * Test script for LLM-based team selection in PMO Assessment endpoint
 */

const testCases = [
  {
    title: "Marketing Campaign Launch",
    description: "Develop a comprehensive marketing campaign for our new SaaS product feature including brand positioning and market analysis",
    expectedTeam: "Marketing"
  },
  {
    title: "Mobile App Development",
    description: "Design and develop a mobile application for customer engagement with modern UI/UX and technical implementation",
    expectedTeam: "SoftwareDesign"
  },
  {
    title: "Sales Strategy Optimization",
    description: "Improve sales strategies and client relationships to increase revenue generation and business development",
    expectedTeam: "Sales"
  },
  {
    title: "Business Process Analysis",
    description: "Analyze current business processes and gather requirements for strategic planning and operational improvements",
    expectedTeam: "BusinessAnalysis"
  },
  {
    title: "Market Research Study",
    description: "Conduct data collection and analysis for a comprehensive market research report on industry trends",
    expectedTeam: "Research"
  }
];

async function testPMOAssessmentLLM() {
  console.log("🧪 Testing LLM-based Team Selection in PMO Assessment Endpoint\n");
  
  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.title}`);
      console.log(`📝 Description: ${testCase.description}`);
      console.log(`🎯 Expected Team: ${testCase.expectedTeam}`);
      
      const response = await fetch('http://localhost:3000/api/pmo-assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          formData: {
            title: testCase.title,
            description: testCase.description,
            priority: 'Medium',
            category: 'Test',
            modelProvider: 'groq',
            modelName: 'deepseek-r1-distill-llama-70b'
          },
          userId: '<EMAIL>'
        })
      });
      
      const result = await response.json();
      
      if (result.selectedTeams) {
        const selectedTeams = result.selectedTeams || [];
        const teamNames = selectedTeams.map(teamId => {
          const teamMapping = {
            'Ag001': 'Marketing',
            'Ag002': 'Research', 
            'Ag003': 'SoftwareDesign',
            'Ag004': 'Sales',
            'Ag005': 'BusinessAnalysis',
            'Ag006': 'InvestigativeResearch'
          };
          return teamMapping[teamId] || teamId;
        });
        
        const isCorrect = teamNames.includes(testCase.expectedTeam);
        const status = isCorrect ? "✅ PASS" : "❌ FAIL";
        
        console.log(`🤖 Selected Team: ${teamNames.join(', ')}`);
        console.log(`💭 Rationale: ${result.teamSelectionRationale || 'No rationale provided'}`);
        console.log(`${status}\n`);
        
      } else {
        console.log(`❌ API Error: ${result.error || 'Unknown error'}\n`);
      }
      
    } catch (error) {
      console.log(`❌ Test Error: ${error.message}\n`);
    }
  }
}

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  testPMOAssessmentLLM().catch(console.error);
}

module.exports = { testCases, testPMOAssessmentLLM };

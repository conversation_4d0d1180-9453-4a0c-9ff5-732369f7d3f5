# LLM-Powered Team Selection Implementation

## Overview

This document describes the implementation of an LLM-powered team selection system that replaces the previous keyword-based logic in the PMO Assessment endpoint (`/api/pmo-assessment/route.ts`).

## Changes Made

### 1. Replaced Keyword-Based Logic

**Before:**
```javascript
// Simple keyword-based team selection
const description = formData.description.toLowerCase();
if (description.includes('market') || description.includes('brand') || description.includes('campaign')) {
  selectedTeams.push(AgenticTeamId.Marketing);
} else if (description.includes('research') || description.includes('analysis') || description.includes('data')) {
  selectedTeams.push(AgenticTeamId.Research);
}
// ... more if/else conditions
```

**After:**
```javascript
// Use LLM-powered team selection instead of keyword-based logic
const teamSelectionResult = await selectTeamWithLLM(formData.description);
const selectedTeams = teamSelectionResult.selectedTeams;
```

### 2. Added LLM Integration

- **Model Used:** `deepseek-r1-distill-llama-70b` via Groq AI
- **Service:** Existing `lib/tools/groq-ai.ts` service
- **Validation:** Zod schema for response validation

### 3. New Functions Added

#### `selectTeamWithLLM(description: string)`
- Sends the project description to the LLM with a structured prompt
- Asks the LLM to select the most appropriate team from available options
- Returns structured response with team selection and rationale
- Includes error handling with fallback to keyword-based selection

#### `selectTeamWithKeywords(description: string)`
- Fallback function that preserves the original keyword-based logic
- Used when LLM call fails or returns invalid response
- Ensures system reliability

### 4. Zod Schema for Validation

```typescript
const TeamSelectionResponseSchema = z.object({
  selectedTeam: z.enum(['Ag001', 'Ag002', 'Ag003', 'Ag004', 'Ag005', 'Ag006']),
  rationale: z.string().optional()
});
```

## Available Teams

The system can select from these agentic teams:

- **Ag001 (Marketing Team):** Marketing strategy, content creation, brand management, market analysis
- **Ag002 (Research Team):** Data collection, analysis, literature reviews, research reports
- **Ag003 (Software Design Team):** Software development, UI/UX design, coding, technical implementation
- **Ag004 (Sales Team):** Sales strategies, client relationships, proposal development, revenue generation
- **Ag005 (Business Analysis Team):** Strategic planning, process analysis, requirements engineering
- **Ag006 (Investigative Research Team):** Comprehensive investigative research using specialized journalist AI agents

## LLM Prompt Structure

The prompt is designed to:
1. Provide clear context about the task
2. List all available teams with their specializations
3. Request a specific response format (JSON)
4. Ask for both team selection and rationale

## Error Handling

The implementation includes multiple layers of error handling:

1. **JSON Parsing Errors:** Falls back to keyword matching
2. **Zod Validation Errors:** Falls back to keyword matching
3. **LLM Service Errors:** Falls back to keyword matching
4. **Network Errors:** Falls back to keyword matching

## Response Format

The API now returns additional information:

```json
{
  "pmoAssessment": "...",
  "selectedTeams": ["Ag001"],
  "teamSelectionRationale": "Selected based on marketing-related content and campaign requirements"
}
```

## Testing

### Unit Tests
- Created `test-team-selection-unit.js` for isolated function testing
- Tests both LLM and fallback keyword selection
- All tests passing ✅

### Integration Tests
- Created `test-pmo-assessment-llm.js` for full endpoint testing
- Tests real API calls with various project descriptions

## Benefits

1. **More Accurate Selection:** LLM can understand context and nuance better than keyword matching
2. **Flexibility:** Can handle complex descriptions that don't fit simple keyword patterns
3. **Transparency:** Provides rationale for team selection decisions
4. **Reliability:** Fallback ensures system continues to work even if LLM fails
5. **Maintainability:** Easier to adjust selection logic through prompt engineering

## Configuration

The LLM selection uses these parameters:
- **Model:** `deepseek-r1-distill-llama-70b`
- **Temperature:** 0.3 (for consistent, focused responses)
- **Max Tokens:** 500 (sufficient for team selection and rationale)

## Future Enhancements

1. **Multi-Team Selection:** Extend to support selecting multiple teams for complex projects
2. **Confidence Scoring:** Add confidence levels to team selections
3. **Learning from Feedback:** Incorporate user feedback to improve selection accuracy
4. **Custom Prompts:** Allow customization of selection criteria through configuration
